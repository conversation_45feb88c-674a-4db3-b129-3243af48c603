import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DocumentMagnifyingGlassIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { CheckCircleIcon } from '@heroicons/react/24/solid';
import UrlInputForm from '../../components/analysis/UrlInputForm';

const Home = () => {
  const navigate = useNavigate();
  const [recentAnalyses, setRecentAnalyses] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleAnalysisSubmit = async (analysisId) => {
    // Navigate to the analysis page to show results
    navigate(`/analysis/${analysisId}`);
  };

  const features = [
    {
      icon: DocumentMagnifyingGlassIcon,
      title: 'Comprehensive Scanning',
      description: 'Automated WCAG 2.1 AA/AAA compliance checking using axe-core engine'
    },
    {
      icon: CheckCircleIcon,
      title: 'Detailed Reports',
      description: 'In-depth violation analysis with actionable remediation recommendations'
    },
    {
      icon: ExclamationTriangleIcon,
      title: 'Real-time Analysis',
      description: 'Live scanning with progress tracking and immediate feedback'
    }
  ];

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <div className="text-center animate-slide-up">
        <h1 className="text-4xl font-bold text-white sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl leading-tight px-4">
          <span className="block">Web Accessibility</span>
          <span className="block text-gradient bg-gradient-to-r from-lovable-blue-400 to-lovable-purple-400 bg-clip-text text-transparent">
            Analyzer
          </span>
        </h1>
        <p className="mt-6 max-w-2xl mx-auto text-base text-white/80 sm:text-lg md:text-xl lg:text-2xl leading-relaxed px-4">
          Comprehensive web accessibility analysis and reporting. Ensure your website meets WCAG guidelines and provides an
          <span className="text-white font-semibold"> inclusive experience</span> for all users.
        </p>
      </div>

      {/* URL Input Form */}
      <div className="max-w-3xl mx-auto animate-scale-in">
        <div className="card-lovable p-8 md:p-10">
          <h2 className="text-3xl font-bold text-white mb-4 text-center">
            Analyze Your Website
          </h2>
          <p className="text-white/70 text-center mb-8 text-lg leading-relaxed">
            Enter a URL to start a comprehensive accessibility analysis
          </p>
          <UrlInputForm onSubmit={handleAnalysisSubmit} />
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-6xl mx-auto">
        <h3 className="text-4xl font-bold text-white text-center mb-12">
          Why Choose Our <span className="text-gradient">Analyzer</span>?
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="card-lovable p-8 text-center group hover:scale-105 transition-all duration-300"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex justify-center mb-6">
                <div className="p-4 rounded-2xl bg-gradient-to-br from-lovable-blue-500 to-lovable-purple-500 shadow-glow">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
              </div>
              <h4 className="text-xl font-bold text-white mb-4">
                {feature.title}
              </h4>
              <p className="text-white/70 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Analyses Section */}
      {recentAnalyses.length > 0 && (
        <div className="max-w-5xl mx-auto">
          <h3 className="text-3xl font-bold text-white mb-8 text-center">
            Recent Public Analyses
          </h3>
          <div className="card-lovable p-6">
            <div className="space-y-4">
              {recentAnalyses.map((analysis, index) => (
                <div key={index} className="flex items-center justify-between border-b border-white/10 pb-4 last:border-b-0">
                  <div>
                    <p className="text-sm font-medium text-white">
                      {analysis.url}
                    </p>
                    <p className="text-sm text-white/60">
                      Analyzed {new Date(analysis.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      analysis.complianceScore >= 90
                        ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                        : analysis.complianceScore >= 70
                        ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
                        : 'bg-red-500/20 text-red-300 border border-red-500/30'
                    }`}>
                      {analysis.complianceScore}% compliant
                    </span>
                    <button
                      onClick={() => navigate(`/analysis/${analysis.id}`)}
                      className="text-lovable-blue-400 hover:text-lovable-blue-300 text-sm font-medium transition-colors duration-200"
                    >
                      View Report
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Getting Started Section */}
      <div className="max-w-3xl mx-auto text-center">
        <div className="card-lovable p-8 border border-white/20">
          <h3 className="text-2xl font-bold text-white mb-4">
            Get Started in <span className="text-gradient">Seconds</span>
          </h3>
          <p className="text-white/80 mb-6 text-lg leading-relaxed">
            Simply enter your website URL above and click "Analyze" to receive a comprehensive accessibility report with actionable recommendations.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center justify-center space-x-2 text-white/70">
              <span className="text-green-400">✓</span>
              <span>No registration required</span>
            </div>
            <div className="flex items-center justify-center space-x-2 text-white/70">
              <span className="text-green-400">✓</span>
              <span>WCAG 2.1 compliance checking</span>
            </div>
            <div className="flex items-center justify-center space-x-2 text-white/70">
              <span className="text-green-400">✓</span>
              <span>Instant remediation guidance</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
