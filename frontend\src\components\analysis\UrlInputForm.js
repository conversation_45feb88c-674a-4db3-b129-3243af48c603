import React, { useState } from 'react';
import { ExclamationCircleIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { analysisService } from '../../services/api/analysisService';
import { useToast } from '../common/Toast';

const UrlInputForm = ({ onSubmit }) => {
  const { showSuccess, showError } = useToast();
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [settings, setSettings] = useState({
    wcagLevel: 'AA',
    includeScreenshots: true,
    isPublic: false
  });

  const validateUrl = (url) => {
    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return 'URL must use HTTP or HTTPS protocol';
      }
      return null;
    } catch (e) {
      return 'Please enter a valid URL (e.g., https://example.com)';
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    // Validate URL
    const urlError = validateUrl(url);
    if (urlError) {
      setError(urlError);
      return;
    }

    setLoading(true);

    try {
      // Create analysis request
      const response = await analysisService.createAnalysis({
        url: url.trim(),
        settings: {
          wcagLevel: settings.wcagLevel,
          includeScreenshots: settings.includeScreenshots,
          timeout: 30000,
          viewport: { width: 1920, height: 1080 }
        },
        isPublic: settings.isPublic
      });

      if (response.data && response.data.data && response.data.data.id) {
        showSuccess('Analysis started successfully! Redirecting to results...');
        // Call the onSubmit callback with the analysis ID
        onSubmit(response.data.data.id);
      } else {
        setError('Failed to create analysis request. Please try again.');
      }
    } catch (err) {
      console.error('Analysis creation error:', err);
      const errorMessage = err.response?.data?.message ||
        'Failed to start analysis. Please check the URL and try again.';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleUrlChange = (e) => {
    setUrl(e.target.value);
    if (error) setError(''); // Clear error when user starts typing
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* URL Input */}
      <div>
        <label htmlFor="url" className="block text-sm font-semibold text-white mb-3">
          Website URL
        </label>
        <div className="relative">
          <input
            type="url"
            id="url"
            value={url}
            onChange={handleUrlChange}
            placeholder="https://example.com"
            className={`input-lovable block w-full text-lg ${
              error ? 'ring-2 ring-red-400/50 border-red-400/30' : ''
            }`}
            disabled={loading}
            required
          />
          {error && (
            <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
              <ExclamationCircleIcon className="h-5 w-5 text-red-400" />
            </div>
          )}
        </div>
        {error && (
          <p className="mt-3 text-sm text-red-400 flex items-center animate-slide-up">
            <ExclamationCircleIcon className="h-4 w-4 mr-2" />
            {error}
          </p>
        )}
      </div>

      {/* Analysis Settings */}
      <div className="space-y-6">
        <h4 className="text-lg font-semibold text-white">Analysis Settings</h4>

        {/* WCAG Level */}
        <div>
          <label htmlFor="wcagLevel" className="block text-sm font-medium text-white/80 mb-2">
            WCAG Compliance Level
          </label>
          <select
            id="wcagLevel"
            value={settings.wcagLevel}
            onChange={(e) => setSettings({ ...settings, wcagLevel: e.target.value })}
            className="block w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 transition-all duration-200"
            disabled={loading}
          >
            <option value="A" className="bg-gray-800 text-white">WCAG 2.1 Level A</option>
            <option value="AA" className="bg-gray-800 text-white">WCAG 2.1 Level AA (Recommended)</option>
            <option value="AAA" className="bg-gray-800 text-white">WCAG 2.1 Level AAA</option>
          </select>
        </div>

        {/* Checkboxes */}
        <div className="space-y-4">
          <label className="flex items-center group cursor-pointer">
            <div className="relative">
              <input
                type="checkbox"
                checked={settings.includeScreenshots}
                onChange={(e) => setSettings({ ...settings, includeScreenshots: e.target.checked })}
                className="sr-only"
                disabled={loading}
              />
              <div className={`w-5 h-5 rounded-md border-2 transition-all duration-200 ${
                settings.includeScreenshots
                  ? 'bg-lovable-blue-500 border-lovable-blue-500'
                  : 'border-white/30 bg-white/10'
              }`}>
                {settings.includeScreenshots && (
                  <svg className="w-3 h-3 text-white absolute top-0.5 left-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
            </div>
            <span className="ml-3 text-sm text-white/80 group-hover:text-white transition-colors duration-200">
              Include screenshots in report
            </span>
          </label>

          <label className="flex items-center group cursor-pointer">
            <div className="relative">
              <input
                type="checkbox"
                checked={settings.isPublic}
                onChange={(e) => setSettings({ ...settings, isPublic: e.target.checked })}
                className="sr-only"
                disabled={loading}
              />
              <div className={`w-5 h-5 rounded-md border-2 transition-all duration-200 ${
                settings.isPublic
                  ? 'bg-lovable-blue-500 border-lovable-blue-500'
                  : 'border-white/30 bg-white/10'
              }`}>
                {settings.isPublic && (
                  <svg className="w-3 h-3 text-white absolute top-0.5 left-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
            </div>
            <span className="ml-3 text-sm text-white/80 group-hover:text-white transition-colors duration-200">
              Make results publicly viewable
            </span>
          </label>
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={loading || !url.trim()}
        className={`btn-lovable w-full flex justify-center items-center px-8 py-4 text-lg font-semibold ${
          loading || !url.trim()
            ? 'opacity-50 cursor-not-allowed transform-none'
            : 'hover:shadow-glow-lg'
        }`}
      >
        {loading ? (
          <>
            <ArrowPathIcon className="animate-spin -ml-1 mr-3 h-5 w-5" />
            Starting Analysis...
          </>
        ) : (
          'Analyze Website'
        )}
      </button>

      {/* Help Text */}
      <div className="text-sm text-white/60 text-center space-y-1">
        <p>Analysis typically takes 15-30 seconds depending on page complexity.</p>
        <p>We'll scan your website for WCAG compliance and provide detailed recommendations.</p>
      </div>
    </form>
  );
};

export default UrlInputForm;
